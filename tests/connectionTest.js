const ContactNumbersService = require('../services/contactNumbers.service');

// Test the improved bulkUpdateVotes function
async function testBulkUpdateVotes() {
  console.log('🧪 Testing bulk update votes function...');
  
  const contactNumbersService = new ContactNumbersService();
  
  // Test data
  const testVotesData = [
    { contactId: 1, upVotes: 10, downVotes: 2 },
    { contactId: 2, upVotes: 5, downVotes: 1 },
    { contactId: 3, upVotes: 15, downVotes: 3 },
  ];

  try {
    console.log('Testing with valid data...');
    const result = await contactNumbersService.bulkUpdateVotes(testVotesData);
    console.log('✅ Bulk update test passed:', result);
  } catch (error) {
    console.error('❌ Bulk update test failed:', error.message);
  }

  // Test with invalid data
  const invalidTestData = [
    { contactId: 'invalid', upVotes: 10, downVotes: 2 },
    { contactId: 2, upVotes: -5, downVotes: 1 },
    { contactId: null, upVotes: 15, downVotes: 3 },
  ];

  try {
    console.log('Testing with invalid data...');
    const result = await contactNumbersService.bulkUpdateVotes(invalidTestData);
    console.log('✅ Invalid data handling test passed:', result);
  } catch (error) {
    console.error('❌ Invalid data handling test failed:', error.message);
  }

  // Test with empty data
  try {
    console.log('Testing with empty data...');
    const result = await contactNumbersService.bulkUpdateVotes([]);
    console.log('✅ Empty data test passed:', result);
  } catch (error) {
    console.error('❌ Empty data test failed:', error.message);
  }
}

// Test connection monitoring
async function testConnectionMonitoring() {
  console.log('🔍 Testing connection monitoring...');
  
  const connectionMonitor = require('../utils/connectionMonitor');
  
  try {
    const health = await connectionMonitor.checkConnectionHealth();
    console.log('✅ Connection health check:', health);
    
    const stats = connectionMonitor.getStats();
    console.log('✅ Connection stats:', stats);
  } catch (error) {
    console.error('❌ Connection monitoring test failed:', error.message);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  async function runTests() {
    console.log('🚀 Starting connection and database tests...\n');
    
    await testConnectionMonitoring();
    console.log('\n' + '='.repeat(50) + '\n');
    await testBulkUpdateVotes();
    
    console.log('\n✨ Tests completed!');
    process.exit(0);
  }
  
  runTests().catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testBulkUpdateVotes,
  testConnectionMonitoring,
};
