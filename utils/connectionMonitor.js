const { sequelize } = require('../config/database');

class ConnectionMonitor {
  constructor() {
    this.connectionStats = {
      active: 0,
      idle: 0,
      total: 0,
      errors: 0,
      lastError: null,
    };
    
    this.startMonitoring();
  }

  startMonitoring() {
    // Monitor connection pool every 30 seconds
    setInterval(() => {
      this.logConnectionStats();
    }, 30000);

    // Listen for connection events
    sequelize.connectionManager.on('connect', () => {
      this.connectionStats.active++;
      this.connectionStats.total++;
    });

    sequelize.connectionManager.on('disconnect', () => {
      this.connectionStats.active--;
    });

    sequelize.connectionManager.on('error', (error) => {
      this.connectionStats.errors++;
      this.connectionStats.lastError = {
        message: error.message,
        timestamp: new Date().toISOString(),
      };
      
      if (error.message.includes('max_user_connections')) {
        console.error('⚠️  CONNECTION LIMIT REACHED - Consider implementing connection throttling');
      }
    });
  }

  logConnectionStats() {
    const pool = sequelize.connectionManager.pool;
    
    if (pool) {
      const stats = {
        size: pool.size || 0,
        available: pool.available || 0,
        using: pool.using || 0,
        waiting: pool.waiting || 0,
      };

      console.log('📊 Connection Pool Stats:', {
        ...stats,
        errors: this.connectionStats.errors,
        lastError: this.connectionStats.lastError,
      });

      // Warn if connection usage is high
      if (stats.using >= 2) { // 2 out of 3 max connections
        console.warn('⚠️  High connection usage detected:', stats);
      }
    }
  }

  async checkConnectionHealth() {
    try {
      await sequelize.authenticate();
      return { healthy: true, message: 'Database connection is healthy' };
    } catch (error) {
      return { 
        healthy: false, 
        message: error.message,
        isConnectionLimit: error.message.includes('max_user_connections')
      };
    }
  }

  getStats() {
    const pool = sequelize.connectionManager.pool;
    return {
      pool: pool ? {
        size: pool.size || 0,
        available: pool.available || 0,
        using: pool.using || 0,
        waiting: pool.waiting || 0,
      } : null,
      errors: this.connectionStats.errors,
      lastError: this.connectionStats.lastError,
    };
  }

  // Helper method to execute database operations with connection monitoring
  async executeWithMonitoring(operation, operationName = 'database operation') {
    const startTime = Date.now();
    
    try {
      console.log(`🔄 Starting ${operationName}`);
      const result = await operation();
      const duration = Date.now() - startTime;
      console.log(`✅ Completed ${operationName} in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Failed ${operationName} after ${duration}ms:`, error.message);
      
      if (error.message.includes('max_user_connections')) {
        console.error('🚨 Connection limit exceeded during:', operationName);
        this.logConnectionStats();
      }
      
      throw error;
    }
  }
}

// Create singleton instance
const connectionMonitor = new ConnectionMonitor();

module.exports = connectionMonitor;
