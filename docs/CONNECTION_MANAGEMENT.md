# Database Connection Management

## Problem Resolved

The application was experiencing `max_user_connections` errors due to inefficient database connection usage. The MySQL user 'india_customer_care' was limited to 5 concurrent connections, but the application was creating multiple concurrent connections that exceeded this limit.

## Root Cause

The primary issue was in the `bulkUpdateVotes` function in `ContactNumbersService`, which used `Promise.all` to execute multiple database updates concurrently. Each update created its own database connection, quickly exhausting the connection pool.

## Solutions Implemented

### 1. Optimized Bulk Update Function

**Before:**
```javascript
// Created multiple concurrent connections
const results = await Promise.all(
  updates.map(({ where, update }) =>
    ContactNumber.update(update, { where })
  )
);
```

**After:**
```javascript
// Single bulk update query using CASE statements
const updateQuery = `
  UPDATE contact_numbers 
  SET 
    upvote_count = CASE ${upvoteCases} ELSE upvote_count END,
    downvote_count = CASE ${downvoteCases} ELSE downvote_count END,
    updated_at = NOW()
  WHERE id IN (${contactIds.join(',')})
`;
```

### 2. Improved Connection Pool Configuration

- Reduced max connections from 5 to 3
- Decreased acquire timeout to 30 seconds
- More aggressive connection eviction (10 seconds)
- Added `handleDisconnects: true`

### 3. Connection Monitoring System

Added `utils/connectionMonitor.js` to:
- Monitor connection pool usage in real-time
- Log connection statistics every 30 seconds
- Detect and alert on connection limit issues
- Provide health check endpoints

### 4. Enhanced Error Handling

- Graceful handling of connection limit errors
- Retry logic with exponential backoff
- Fallback to sequential updates when bulk update fails
- Better error messages for clients

### 5. Health Check Endpoints

- `GET /health` - Overall application health
- `GET /health/database` - Database connection statistics

## Key Improvements

1. **Single Transaction Approach**: All vote updates now happen in a single transaction
2. **Input Validation**: Added validation to prevent SQL injection
3. **Fallback Mechanism**: Sequential updates as fallback when bulk update fails
4. **Connection Monitoring**: Real-time monitoring of connection usage
5. **Better Error Handling**: Graceful degradation instead of complete failure

## Monitoring

The connection monitor provides:
- Real-time connection pool statistics
- Error tracking and alerting
- Performance recommendations
- Health check endpoints for monitoring tools

## Testing

Run the connection tests:
```bash
node tests/connectionTest.js
```

## Best Practices Going Forward

1. **Always use transactions** for bulk operations
2. **Avoid Promise.all** with database operations that could create many connections
3. **Monitor connection usage** regularly through health endpoints
4. **Implement retry logic** for connection errors
5. **Use bulk operations** instead of multiple individual queries
6. **Validate input data** to prevent SQL injection

## Monitoring Commands

Check connection health:
```bash
curl http://localhost:5000/health
curl http://localhost:5000/health/database
```

## Configuration

The connection pool is configured in `config/database.js`:
```javascript
pool: {
  max: 3,           // Maximum connections
  min: 1,           // Minimum connections
  acquire: 30000,   // Connection acquire timeout
  idle: 30000,      // Idle timeout
  evict: 10000,     // Eviction timeout
}
```

## Future Considerations

1. Consider increasing MySQL `max_user_connections` if needed
2. Implement connection throttling for high-traffic scenarios
3. Add database query performance monitoring
4. Consider read replicas for read-heavy operations
