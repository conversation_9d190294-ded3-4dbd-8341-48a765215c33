const { options } = require('sequelize/lib/model');
const { ContactNumber } = require('../database/schemas');

class ContactNumbersService {
  numberInstance(number) {
    return {
      numberId: number.id,
      companyId: number.company_id,
      number: number.contact_number,
      description: number.contact_description || null,
      type: number.contact_type || null,
      upvoteCount: number.upvote_count,
      downvoteCount: number.downvote_count,
      isWhatsapp: number.is_whatsapp,
    };
  }

  async findNumber(filter) {
    return await ContactNumber.findOne({ where: filter, raw: false });
  }

  async findAllNumbers(filter = {}, options = {}) {
    return await ContactNumber.findAll({ where: filter, ...options });
  }

  async createNumber(data) {
    return await ContactNumber.create(data);
  }

  async createContactNumbers(data, options = {}) {
    return await ContactNumber.bulkCreate(data, { ...options });
  }

  async updateNumber(filter, data) {
    return await ContactNumber.update(data, { where: filter });
  }

  async deleteNumber(filter, options = {}) {
    return await ContactNumber.destroy({ where: filter, ...options });
  }

  async truncateAll(transaction) {
    return await ContactNumber.destroy({
      where: {},
      truncate: true,
      cascade: true,
      transaction,
    });
  }

  async bulkCreateContactNumbers(contactNumbers, options = {}) {
    return await ContactNumber.bulkCreate(contactNumbers, options);
  }

  async bulkUpdateVotes(votesData) {
    if (!votesData || votesData.length === 0) {
      return [];
    }

    const transaction = await global.sequelize.transaction();

    try {
      // Use a single bulk update query with CASE statements for efficiency
      // This approach uses only one database connection instead of multiple concurrent ones

      // Validate and sanitize input data
      const validVotesData = votesData.filter(vote =>
        vote.contactId &&
        Number.isInteger(vote.contactId) &&
        vote.contactId > 0 &&
        Number.isInteger(vote.upVotes) &&
        vote.upVotes >= 0 &&
        Number.isInteger(vote.downVotes) &&
        vote.downVotes >= 0
      );

      if (validVotesData.length === 0) {
        await transaction.commit();
        return [];
      }

      const contactIds = validVotesData.map(vote => vote.contactId);

      // Build CASE statements for bulk update with proper escaping
      const upvoteCases = validVotesData.map(vote =>
        `WHEN id = ${parseInt(vote.contactId)} THEN ${parseInt(vote.upVotes)}`
      ).join(' ');

      const downvoteCases = validVotesData.map(vote =>
        `WHEN id = ${parseInt(vote.contactId)} THEN ${parseInt(vote.downVotes)}`
      ).join(' ');

      const updateQuery = `
        UPDATE contact_numbers
        SET
          upvote_count = CASE ${upvoteCases} ELSE upvote_count END,
          downvote_count = CASE ${downvoteCases} ELSE downvote_count END,
          updated_at = NOW()
        WHERE id IN (${contactIds.map(id => parseInt(id)).join(',')})
      `;

      const result = await global.sequelize.query(updateQuery, {
        type: global.sequelize.QueryTypes.UPDATE,
        transaction
      });

      await transaction.commit();

      console.log(`Successfully updated votes for ${votesData.length} contact numbers`);
      return result;

    } catch (error) {
      console.error('Error updating vote counts:', error);

      if (!transaction.finished) {
        await transaction.rollback();
      }

      // Implement retry logic for connection errors
      if (error.message && error.message.includes('max_user_connections')) {
        console.log('Connection limit reached, retrying after delay...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Retry once with exponential backoff
        try {
          return await this.bulkUpdateVotesSequential(votesData);
        } catch (retryError) {
          console.error('Retry failed:', retryError);
          throw retryError;
        }
      }

      throw error;
    }
  }

  // Fallback method for sequential updates when bulk update fails
  async bulkUpdateVotesSequential(votesData) {
    if (!votesData || votesData.length === 0) {
      return [];
    }

    const transaction = await global.sequelize.transaction();
    const results = [];

    try {
      // Process updates sequentially to avoid connection exhaustion
      for (const vote of votesData) {
        const result = await ContactNumber.update(
          {
            upvote_count: vote.upVotes,
            downvote_count: vote.downVotes,
          },
          {
            where: { id: vote.contactId },
            transaction,
          }
        );
        results.push(result);
      }

      await transaction.commit();
      console.log(
        `Successfully updated votes sequentially for ${votesData.length} contact numbers`
      );
      return results;

    } catch (error) {
      console.error('Error in sequential vote updates:', error);

      if (!transaction.finished) {
        await transaction.rollback();
      }

      throw error;
    }
  }
}

module.exports = ContactNumbersService;
